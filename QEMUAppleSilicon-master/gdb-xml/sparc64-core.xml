<?xml version="1.0"?>
<!-- Copyright (C) 2013-2025 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.sparc.core">
  <reg name="g0" bitsize="64" type="uint64" regnum="0"/>
  <reg name="g1" bitsize="64" type="uint64" regnum="1"/>
  <reg name="g2" bitsize="64" type="uint64" regnum="2"/>
  <reg name="g3" bitsize="64" type="uint64" regnum="3"/>
  <reg name="g4" bitsize="64" type="uint64" regnum="4"/>
  <reg name="g5" bitsize="64" type="uint64" regnum="5"/>
  <reg name="g6" bitsize="64" type="uint64" regnum="6"/>
  <reg name="g7" bitsize="64" type="uint64" regnum="7"/>
  <reg name="o0" bitsize="64" type="uint64" regnum="8"/>
  <reg name="o1" bitsize="64" type="uint64" regnum="9"/>
  <reg name="o2" bitsize="64" type="uint64" regnum="10"/>
  <reg name="o3" bitsize="64" type="uint64" regnum="11"/>
  <reg name="o4" bitsize="64" type="uint64" regnum="12"/>
  <reg name="o5" bitsize="64" type="uint64" regnum="13"/>
  <reg name="sp" bitsize="64" type="uint64" regnum="14"/>
  <reg name="o7" bitsize="64" type="uint64" regnum="15"/>
  <reg name="l0" bitsize="64" type="uint64" regnum="16"/>
  <reg name="l1" bitsize="64" type="uint64" regnum="17"/>
  <reg name="l2" bitsize="64" type="uint64" regnum="18"/>
  <reg name="l3" bitsize="64" type="uint64" regnum="19"/>
  <reg name="l4" bitsize="64" type="uint64" regnum="20"/>
  <reg name="l5" bitsize="64" type="uint64" regnum="21"/>
  <reg name="l6" bitsize="64" type="uint64" regnum="22"/>
  <reg name="l7" bitsize="64" type="uint64" regnum="23"/>
  <reg name="i0" bitsize="64" type="uint64" regnum="24"/>
  <reg name="i1" bitsize="64" type="uint64" regnum="25"/>
  <reg name="i2" bitsize="64" type="uint64" regnum="26"/>
  <reg name="i3" bitsize="64" type="uint64" regnum="27"/>
  <reg name="i4" bitsize="64" type="uint64" regnum="28"/>
  <reg name="i5" bitsize="64" type="uint64" regnum="29"/>
  <reg name="fp" bitsize="64" type="uint64" regnum="30"/>
  <reg name="i7" bitsize="64" type="uint64" regnum="31"/>

  <reg name="f0" bitsize="32" type="ieee_single" regnum="32"/>
  <reg name="f1" bitsize="32" type="ieee_single" regnum="33"/>
  <reg name="f2" bitsize="32" type="ieee_single" regnum="34"/>
  <reg name="f3" bitsize="32" type="ieee_single" regnum="35"/>
  <reg name="f4" bitsize="32" type="ieee_single" regnum="36"/>
  <reg name="f5" bitsize="32" type="ieee_single" regnum="37"/>
  <reg name="f6" bitsize="32" type="ieee_single" regnum="38"/>
  <reg name="f7" bitsize="32" type="ieee_single" regnum="39"/>
  <reg name="f8" bitsize="32" type="ieee_single" regnum="40"/>
  <reg name="f9" bitsize="32" type="ieee_single" regnum="41"/>
  <reg name="f10" bitsize="32" type="ieee_single" regnum="42"/>
  <reg name="f11" bitsize="32" type="ieee_single" regnum="43"/>
  <reg name="f12" bitsize="32" type="ieee_single" regnum="44"/>
  <reg name="f13" bitsize="32" type="ieee_single" regnum="45"/>
  <reg name="f14" bitsize="32" type="ieee_single" regnum="46"/>
  <reg name="f15" bitsize="32" type="ieee_single" regnum="47"/>
  <reg name="f16" bitsize="32" type="ieee_single" regnum="48"/>
  <reg name="f17" bitsize="32" type="ieee_single" regnum="49"/>
  <reg name="f18" bitsize="32" type="ieee_single" regnum="50"/>
  <reg name="f19" bitsize="32" type="ieee_single" regnum="51"/>
  <reg name="f20" bitsize="32" type="ieee_single" regnum="52"/>
  <reg name="f21" bitsize="32" type="ieee_single" regnum="53"/>
  <reg name="f22" bitsize="32" type="ieee_single" regnum="54"/>
  <reg name="f23" bitsize="32" type="ieee_single" regnum="55"/>
  <reg name="f24" bitsize="32" type="ieee_single" regnum="56"/>
  <reg name="f25" bitsize="32" type="ieee_single" regnum="57"/>
  <reg name="f26" bitsize="32" type="ieee_single" regnum="58"/>
  <reg name="f27" bitsize="32" type="ieee_single" regnum="59"/>
  <reg name="f28" bitsize="32" type="ieee_single" regnum="60"/>
  <reg name="f29" bitsize="32" type="ieee_single" regnum="61"/>
  <reg name="f30" bitsize="32" type="ieee_single" regnum="62"/>
  <reg name="f31" bitsize="32" type="ieee_single" regnum="63"/>

  <reg name="f32" bitsize="64" type="ieee_double" regnum="64"/>
  <reg name="f34" bitsize="64" type="ieee_double" regnum="65"/>
  <reg name="f36" bitsize="64" type="ieee_double" regnum="66"/>
  <reg name="f38" bitsize="64" type="ieee_double" regnum="67"/>
  <reg name="f40" bitsize="64" type="ieee_double" regnum="68"/>
  <reg name="f42" bitsize="64" type="ieee_double" regnum="69"/>
  <reg name="f44" bitsize="64" type="ieee_double" regnum="70"/>
  <reg name="f46" bitsize="64" type="ieee_double" regnum="71"/>
  <reg name="f48" bitsize="64" type="ieee_double" regnum="72"/>
  <reg name="f50" bitsize="64" type="ieee_double" regnum="73"/>
  <reg name="f52" bitsize="64" type="ieee_double" regnum="74"/>
  <reg name="f54" bitsize="64" type="ieee_double" regnum="75"/>
  <reg name="f56" bitsize="64" type="ieee_double" regnum="76"/>
  <reg name="f58" bitsize="64" type="ieee_double" regnum="77"/>
  <reg name="f60" bitsize="64" type="ieee_double" regnum="78"/>
  <reg name="f62" bitsize="64" type="ieee_double" regnum="79"/>

  <reg name="pc" bitsize="64" type="code_ptr" regnum="80"/>
  <reg name="npc" bitsize="64" type="code_ptr" regnum="81"/>
  <reg name="state" bitsize="64" type="uint64" regnum="82"/>
  <reg name="fsr" bitsize="64" type="uint64" regnum="83"/>
  <reg name="fprs" bitsize="64" type="uint64" regnum="84"/>
  <reg name="y" bitsize="64" type="uint64" regnum="85"/>
</feature>
