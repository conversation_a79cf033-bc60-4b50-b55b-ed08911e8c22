
variables:
  # On stable branches this is changed by later rules. Should also
  # be overridden per pipeline if running pipelines concurrently
  # for different branches in contributor forks.
  QEMU_CI_CONTAINER_TAG: latest

  # For purposes of CI rules, upstream is the gitlab.com/qemu-project
  # namespace. When testing CI, it might be usefult to override this
  # to point to a fork repo
  QEMU_CI_UPSTREAM: qemu-project

# The order of rules defined here is critically important.
# They are evaluated in order and first match wins.
#
# Thus we group them into a number of stages, ordered from
# most restrictive to least restrictive
#
# For pipelines running for stable "staging-X.Y" branches
# we must override QEMU_CI_CONTAINER_TAG
#
.base_job_template:
  variables:
    # Each script line from will be in a collapsible section in the job output
    # and show the duration of each line.
    FF_SCRIPT_SECTIONS: 1
    # The project has a fairly fat GIT repo so we try and avoid bringing in things
    # we don't need. The --filter options avoid blobs and tree references we aren't going to use
    # and we also avoid fetching tags.
    GIT_FETCH_EXTRA_FLAGS: --filter=blob:none --filter=tree:0 --no-tags --prune --quiet

  interruptible: true

  rules:
    #############################################################
    # Stage 1: exclude scenarios where we definitely don't
    # want jobs to run
    #############################################################

    # Never run jobs upstream on stable branch, staging branch jobs already ran
    - if: '$CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_COMMIT_BRANCH =~ /^stable-/'
      when: never

    # Never run jobs upstream on tags, staging branch jobs already ran
    - if: '$CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_COMMIT_TAG'
      when: never

    # Scheduled runs on mainline don't get pipelines except for the special Coverity job
    - if: '$CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_PIPELINE_SOURCE == "schedule"'
      when: never

    # Cirrus jobs can't run unless the creds / target repo are set
    - if: '$QEMU_JOB_CIRRUS && ($CIRRUS_GITHUB_REPO == null || $CIRRUS_API_TOKEN == null)'
      when: never

    # Publishing jobs should only run on the default branch in upstream
    - if: '$QEMU_JOB_PUBLISH == "1" && $CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH'
      when: never

    # Non-publishing jobs should only run on staging branches in upstream
    - if: '$QEMU_JOB_PUBLISH != "1" && $CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_COMMIT_BRANCH !~ /staging/'
      when: never

    # Jobs only intended for forks should always be skipped on upstream
    - if: '$QEMU_JOB_ONLY_FORKS == "1" && $CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM'
      when: never

    # Forks don't get pipelines unless QEMU_CI=1 or QEMU_CI=2 is set
    - if: '$QEMU_CI != "1" && $QEMU_CI != "2" && $CI_PROJECT_NAMESPACE != $QEMU_CI_UPSTREAM'
      when: never


    #############################################################
    # Stage 2: fine tune execution of jobs in specific scenarios
    # where the catch all logic is inappropriate
    #############################################################

    # Optional jobs should not be run unless manually triggered
    - if: '$QEMU_JOB_OPTIONAL && $CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_COMMIT_BRANCH =~ /staging-[[:digit:]]+\.[[:digit:]]/'
      when: manual
      allow_failure: true
      variables:
        QEMU_CI_CONTAINER_TAG: $CI_COMMIT_REF_SLUG

    - if: '$QEMU_JOB_OPTIONAL'
      when: manual
      allow_failure: true

    # Skipped jobs should not be run unless manually triggered
    - if: '$QEMU_JOB_SKIPPED && $CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_COMMIT_BRANCH =~ /staging-[[:digit:]]+\.[[:digit:]]/'
      when: manual
      allow_failure: true
      variables:
        QEMU_CI_CONTAINER_TAG: $CI_COMMIT_REF_SLUG

    - if: '$QEMU_JOB_SKIPPED'
      when: manual
      allow_failure: true

    # Functional jobs can be manually started in forks
    - if: '$QEMU_JOB_FUNCTIONAL && $QEMU_CI_FUNCTIONAL != "1" && $CI_PROJECT_NAMESPACE != $QEMU_CI_UPSTREAM'
      when: manual
      allow_failure: true


    #############################################################
    # Stage 3: catch all logic applying to any job not matching
    # an earlier criteria
    #############################################################

    # Forks pipeline jobs don't start automatically unless
    # QEMU_CI=2 is set
    - if: '$QEMU_CI != "2" && $CI_PROJECT_NAMESPACE != $QEMU_CI_UPSTREAM'
      when: manual

    # Upstream pipeline jobs start automatically unless told not to
    # by setting QEMU_CI=1
    - if: '$QEMU_CI == "1" && $CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_COMMIT_BRANCH =~ /staging-[[:digit:]]+\.[[:digit:]]/'
      when: manual
      variables:
        QEMU_CI_CONTAINER_TAG: $CI_COMMIT_REF_SLUG

    - if: '$QEMU_CI == "1" && $CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM'
      when: manual

    # Jobs can run if any jobs they depend on were successful
    - if: '$CI_PROJECT_NAMESPACE == $QEMU_CI_UPSTREAM && $CI_COMMIT_BRANCH =~ /staging-[[:digit:]]+\.[[:digit:]]/'
      when: on_success
      variables:
        QEMU_CI_CONTAINER_TAG: $CI_COMMIT_REF_SLUG

    - when: on_success
