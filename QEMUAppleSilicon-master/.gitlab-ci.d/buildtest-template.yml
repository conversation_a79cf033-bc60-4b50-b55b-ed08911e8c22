.native_build_job_template:
  extends: .base_job_template
  stage: build
  image: $CI_REGISTRY_IMAGE/qemu/$IMAGE:$QEMU_CI_CONTAINER_TAG
  cache:
    paths:
      - ccache
    key: "$CI_JOB_NAME"
    when: always
  before_script:
    - source scripts/ci/gitlab-ci-section
    - section_start setup "Pre-script setup"
    - JOBS=$(expr $(nproc) + 1)
    - cat /packages.txt
    - section_end setup
  script:
    - export CCACHE_BASEDIR="$(pwd)"
    - export CCACHE_DIR="$CCACHE_BASEDIR/ccache"
    - export CCACHE_MAXSIZE="500M"
    - export PATH="$CCACHE_WRAPPERSDIR:$PATH"
    - du -sh .git
    - mkdir build
    - cd build
    - ccache --zero-stats
    - section_start configure "Running configure"
    - ../configure --enable-werror --disable-docs --enable-fdt=system
          --disable-debug-info
          ${TARGETS:+--target-list="$TARGETS"}
          $CONFIGURE_ARGS ||
      { cat config.log meson-logs/meson-log.txt && exit 1; }
    - if test -n "$LD_JOBS";
      then
        pyvenv/bin/meson configure . -Dbackend_max_links="$LD_JOBS" ;
      fi || exit 1;
    - section_end configure
    - section_start build "Building QEMU"
    - $MAKE -j"$JOBS"
    - section_end build
    - section_start test "Running tests"
    - if test -n "$MAKE_CHECK_ARGS";
      then
        $MAKE -j"$JOBS" $MAKE_CHECK_ARGS ;
      fi
    - section_end test
    - ccache --show-stats

# We jump some hoops in common_test_job_template to avoid
# rebuilding all the object files we skip in the artifacts
.native_build_artifact_template:
  artifacts:
    when: on_success
    expire_in: 2 days
    paths:
      - build
      - .git-submodule-status
    exclude:
      - build/**/*.p
      - build/**/*.a.p
      - build/**/*.c.o
      - build/**/*.c.o.d

.common_test_job_template:
  extends: .base_job_template
  stage: test
  image: $CI_REGISTRY_IMAGE/qemu/$IMAGE:$QEMU_CI_CONTAINER_TAG
  script:
    - source scripts/ci/gitlab-ci-section
    - section_start buildenv "Setting up to run tests"
    - scripts/git-submodule.sh update roms/SLOF
    - build/pyvenv/bin/meson subprojects download $(cd build/subprojects && echo *)
    - cd build
    - find . -type f -exec touch {} +
    # Avoid recompiling by hiding ninja with NINJA=":"
    # We also have to pre-cache the functional tests manually in this case
    - if [ "x${QEMU_TEST_CACHE_DIR}" != "x" ]; then
        $MAKE precache-functional ;
      fi
    - section_end buildenv
    - section_start test "Running tests"
    # doctests need all the compilation artifacts
    - $MAKE NINJA=":" MTESTARGS="--no-suite doc" $MAKE_CHECK_ARGS
    - section_end test

.native_test_job_template:
  extends: .common_test_job_template
  artifacts:
    name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
    when: always
    expire_in: 7 days
    paths:
      - build/meson-logs/testlog.txt
    reports:
      junit: build/meson-logs/testlog.junit.xml

.functional_test_job_template:
  extends: .common_test_job_template
  cache:
    key: "${CI_JOB_NAME}-cache"
    paths:
      - ${CI_PROJECT_DIR}/functional-cache
    policy: pull-push
  artifacts:
    name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
    when: always
    expire_in: 7 days
    paths:
      - build/tests/results/latest/results.xml
      - build/tests/results/latest/test-results
      - build/tests/functional/*/*/*.log
    reports:
      junit: build/tests/results/latest/results.xml
  before_script:
    - export QEMU_TEST_ALLOW_UNTRUSTED_CODE=1
    - export QEMU_TEST_CACHE_DIR=${CI_PROJECT_DIR}/functional-cache
  after_script:
    - cd build
    - du -chs ${CI_PROJECT_DIR}/*-cache
  variables:
    QEMU_JOB_FUNCTIONAL: 1

.wasm_build_job_template:
  extends: .base_job_template
  stage: build
  image: $CI_REGISTRY_IMAGE/qemu/$IMAGE:$QEMU_CI_CONTAINER_TAG
  before_script:
    - source scripts/ci/gitlab-ci-section
    - section_start setup "Pre-script setup"
    - JOBS=$(expr $(nproc) + 1)
    - section_end setup
  script:
    - du -sh .git
    - mkdir build
    - cd build
    - section_start configure "Running configure"
    - emconfigure ../configure --disable-docs
          ${TARGETS:+--target-list="$TARGETS"}
          $CONFIGURE_ARGS ||
      { cat config.log meson-logs/meson-log.txt && exit 1; }
    - if test -n "$LD_JOBS";
      then
        pyvenv/bin/meson configure . -Dbackend_max_links="$LD_JOBS" ;
      fi || exit 1;
    - section_end configure
    - section_start build "Building QEMU"
    - emmake make -j"$JOBS"
    - section_end build
