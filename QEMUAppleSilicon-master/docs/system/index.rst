.. _System Emulation:

----------------
System Emulation
----------------

This section of the manual is the overall guide for users using QEMU
for full system emulation (as opposed to user-mode emulation).
This includes working with hypervisors such as KVM, Xen
or Hypervisor.Framework.

.. toctree::
   :maxdepth: 3

   introduction
   invocation
   device-emulation
   keys
   mux-chardev
   monitor
   images
   virtio-net-failover
   linuxboot
   generic-loader
   guest-loader
   barrier
   vnc-security
   tls
   secrets
   authz
   gdb
   replay
   managed-startup
   bootindex
   cpu-hotplug
   pr-manager
   targets
   security
   multi-process
   confidential-guest-support
   igvm
   vm-templating
   sriov
