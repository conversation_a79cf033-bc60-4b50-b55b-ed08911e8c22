name: build
on:
  push:
    branches:
      - master
  pull_request:

jobs:
  build_linux:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y build-essential libtool meson ninja-build pkg-config libcapstone-dev device-tree-compiler libglib2.0-dev gnutls-bin libjpeg-turbo8-dev libpng-dev libslirp-dev libssh-dev libusb-1.0-0-dev liblzo2-dev libncurses5-dev libpixman-1-dev libsnappy-dev vde2 zstd libgnutls28-dev libgmp10 libgmp3-dev lzfse liblzfse-dev
      - name: Build nettle 3.10.1
        run: |
          wget https://ftp.gnu.org/gnu/nettle/nettle-3.10.1.tar.gz
          tar -xvf nettle-3.10.1.tar.gz
          cd nettle-3.10.1
          ./configure
          make -j$(nproc)
          sudo make install
      - name: Build
        run: |
          mkdir build
          cd build
          PKG_CONFIG_PATH=/usr/local/lib64/pkgconfig ../configure --target-list=aarch64-softmmu,x86_64-softmmu --enable-capstone --enable-slirp --enable-lzfse --enable-nettle --enable-gnutls --disable-werror
          make -j$(nproc)
      - name: Install
        run: |
          cd build
          make DESTDIR=$GITHUB_WORKSPACE/bin/ install
      - name: Publish binaries
        uses: actions/upload-artifact@v4
        with:
          name: linux
          path: ${{ github.workspace }}/bin/

  build_macos:
    runs-on: macos-15
    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0
      - name: Install dependencies
        run: |
          brew install libtool meson ninja pkgconf capstone dtc glib gnutls jpeg-turbo libpng libslirp libssh libusb lzo ncurses nettle pixman snappy vde zstd lzfse nettle
      - name: Build
        run: |
          mkdir build && cd build
          LIBTOOL="glibtool" ../configure --target-list=aarch64-softmmu,x86_64-softmmu --disable-bsd-user --disable-guest-agent --enable-lzfse --enable-slirp --enable-capstone --enable-curses --enable-libssh --enable-virtfs --enable-zstd --extra-cflags=-DNCURSES_WIDECHAR=1 --disable-sdl --disable-gtk --enable-cocoa --enable-lzfse --enable-nettle --enable-gnutls --disable-werror --extra-cflags="-I/opt/homebrew/include" --extra-ldflags="-L/opt/homebrew/lib"
          make -j$(sysctl -n hw.logicalcpu)
      - name: Install
        run: |
          cd build
          make DESTDIR=$GITHUB_WORKSPACE/bin/ install
      - name: Publish binaries
        uses: actions/upload-artifact@v4
        with:
          name: macos_aarch64
          path: ${{ github.workspace }}/bin/

  build_macos_x86_64:
    runs-on: macos-13
    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0
      - name: Install dependencies
        run: |
          brew install libtool meson ninja pkgconf capstone dtc glib gnutls jpeg-turbo libpng libslirp libssh libusb lzo ncurses nettle pixman snappy vde zstd lzfse nettle
      - name: Build
        run: |
          mkdir build && cd build
          LIBTOOL="glibtool" ../configure --target-list=aarch64-softmmu,x86_64-softmmu --disable-bsd-user --disable-guest-agent --enable-lzfse --enable-slirp --enable-capstone --enable-curses --enable-libssh --enable-virtfs --enable-zstd --extra-cflags=-DNCURSES_WIDECHAR=1 --disable-sdl --disable-gtk --enable-cocoa --enable-lzfse --enable-nettle --enable-gnutls --disable-werror --extra-cflags="-I/opt/homebrew/include" --extra-ldflags="-L/opt/homebrew/lib"
          make -j$(sysctl -n hw.logicalcpu)
      - name: Install
        run: |
          cd build
          make DESTDIR=$GITHUB_WORKSPACE/bin/ install
      - name: Publish binaries
        uses: actions/upload-artifact@v4
        with:
          name: macos_x86_64
          path: ${{ github.workspace }}/bin/
